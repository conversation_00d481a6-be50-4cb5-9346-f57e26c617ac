package com.ybmmarket20.adapter

import android.content.Intent
import androidx.appcompat.widget.AppCompatTextView
import android.text.SpannableString
import android.text.TextUtils
import com.ybm.app.adapter.YBMBaseHolder
import com.ybmmarketkotlin.adapter.YBMBaseMultiItemAdapter
import com.ybmmarket20.R
import com.ybmmarket20.bean.LabelIconBean
import com.ybmmarket20.business.correction.ext.priceSpan
import com.ybmmarket20.business.snapshot.entity.SnapshotItemModel
import com.ybmmarket20.business.snapshot.ui.TradingSnapshotDetailActivity
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.StringUtil
import com.ybmmarket20.utils.UiUtils
import com.ybmmarket20.view.TradingSnapshotTagView

/* 
 * Created by sean on 2019-07-26
 * 交易快照列表页适配器
 */

class TradingSnapshotListAdapter(defData: ArrayList<SnapshotItemModel>) : YBMBaseMultiItemAdapter<SnapshotItemModel>(defData) {


    init {
        addItemType(SnapshotItemModel.ITEMTYPE_PACKAGE_HEADER, R.layout.item_snapshot_package_header)
        addItemType(SnapshotItemModel.ITEMTYPE_PACKAGE_FOOTER, R.layout.item_snapshot_package_footer)
        addItemType(SnapshotItemModel.ITEMTYPE_PACKAGE_CONTENT, R.layout.item_snapshot_package_content)
        addItemType(SnapshotItemModel.ITEMTYPE_CONTENT, R.layout.item_snapshot_package_content)
    }


    override fun bindItemView(baseViewHolder: YBMBaseHolder, bean: SnapshotItemModel) {
        when (bean.itemType) {
            SnapshotItemModel.ITEMTYPE_PACKAGE_HEADER -> {
                bindHeader(baseViewHolder, bean)
            }
            SnapshotItemModel.ITEMTYPE_PACKAGE_FOOTER -> {
                bindFooter(baseViewHolder, bean)
            }
            SnapshotItemModel.ITEMTYPE_PACKAGE_CONTENT,
            SnapshotItemModel.ITEMTYPE_CONTENT -> {
                bindContent(baseViewHolder, bean)
            }
        }

    }

    private fun bindHeader(baseViewHolder: YBMBaseHolder, bean: SnapshotItemModel) {
        val packageNameTv = baseViewHolder.getView<AppCompatTextView>(R.id.packageNameTv)
        packageNameTv.text = bean.productName
        baseViewHolder.setText(R.id.packageNameTv, bean.productName)
        baseViewHolder.setText(R.id.packageNumTv, mContext.resources.getString(R.string.trading_snapshot_goods_num, bean.packageCount.toString()))
    }

    private fun bindFooter(baseViewHolder: YBMBaseHolder, bean: SnapshotItemModel) {
        baseViewHolder.setText(R.id.packagePriceTv, SpannableString(mContext.resources.getString(R.string.snapshot_package_footer_price, UiUtils.transform(bean.totalPrice.toString()))).priceSpan())
        baseViewHolder.setText(R.id.packageTotalTv, SpannableString(mContext.resources.getString(R.string.snapshot_package_total, UiUtils.transform(bean.subtotalPrice.toString()))).priceSpan())
    }

    private fun bindContent(baseViewHolder: YBMBaseHolder, bean: SnapshotItemModel) {
        baseViewHolder.setImageUrl(R.id.snapshotIv, AppNetConfig.LORD_IMAGE + bean.imageUrl, R.drawable.jiazaitu_min)
        baseViewHolder.setText(R.id.snapshotNameTv, bean.productName)
        baseViewHolder.setText(R.id.snapshotPriceTv, mContext.resources.getString(R.string.snapshot_package_price, UiUtils.transform(bean.productPrice.toString())))
        baseViewHolder.setText(R.id.snapshotSpecTv, mContext.resources.getString(R.string.snapshot_spec, bean.spec))
        baseViewHolder.setText(R.id.snapshotNumTv, mContext.resources.getString(R.string.trading_snapshot_goods_num, bean.productAmount.toString()))
        baseViewHolder.setText(R.id.snapshotTotalTv, SpannableString(mContext.resources.getString(R.string.snapshot_package_total, UiUtils.transform(bean.subTotal.toString()))).priceSpan())
        val tagView = baseViewHolder.getView<TradingSnapshotTagView>(R.id.activityTagView)
        if (bean.tagList!!.isNotEmpty()) {
            val lableTagList = java.util.ArrayList<LabelIconBean>()
            for ((index) in bean.tagList!!.withIndex()) {
                val lable = LabelIconBean()
                lable.name = bean.tagList!![index].name
                lable.uiType = bean.tagList!![index].uiType
                lableTagList.add(lable)
            }
            tagView.bindData(lableTagList)
        }
        baseViewHolder.setOnClickListener(R.id.goSnapshotDetail) {
            val intent = Intent(mContext, TradingSnapshotDetailActivity::class.java)
            intent.putExtra(IntentCanst.SNAPSHOT_ID, bean.id.toString())
            mContext.startActivity(intent)
        }
        //近效期&远效期
        baseViewHolder.setGone(R.id.ll_validity_period,true)
        if (StringUtil.isEmpty(bean.nearEffect) || "-" == bean.nearEffect || StringUtil.isEmpty(bean.farEffect) || "-" == bean.farEffect) {
            baseViewHolder.setText(R.id.tv_validity_period,"-")
        } else {
            baseViewHolder.setText(R.id.tv_validity_period,bean.nearEffect+"/"+bean.farEffect)
        }
    }

    private fun getChannelCodeStr(code: String): String {
        if (TextUtils.isEmpty(code)) {
            return "药帮忙"
        }
        when (code) {
            "1" -> return "药帮忙"
            "2" -> return "宜块钱"
            else -> return "药帮忙"
        }
    }

}