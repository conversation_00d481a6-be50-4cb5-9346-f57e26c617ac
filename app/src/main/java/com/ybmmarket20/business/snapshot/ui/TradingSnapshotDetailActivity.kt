package com.ybmmarket20.business.snapshot.ui

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.drawable.Drawable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.SimpleItemAnimator
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.GlideDrawable
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.activity.ProductDetailActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CxTag
import com.ybmmarket20.bean.ImagesVideos
import com.ybmmarket20.bean.ImagesVideosListBean
import com.ybmmarket20.bean.TradingSnapshotDetail
import com.ybmmarket20.business.correction.tools.modifyVisibility
import com.ybmmarket20.business.correction.widget.CenterAlignImageSpan
import com.ybmmarket20.business.snapshot.adapter.PromotionAdapter
import com.ybmmarket20.business.snapshot.ext.makeImageUrl
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.UiUtils
import kotlinx.android.synthetic.main.activity_trading_snapshot_detail.*

/*
 * Created by sean on 2019-08-06
 * 交易快照详情页
 */

class TradingSnapshotDetailActivity : BaseActivity(), View.OnClickListener {
    private lateinit var skuId: String

    private var cxTags: ArrayList<CxTag> = ArrayList()
    private lateinit var adapter: PromotionAdapter
    private lateinit var name: SpannableString

    override fun getContentViewId() = R.layout.activity_trading_snapshot_detail

    override fun initData() {
        setTitle(resources.getString(R.string.trading_snapshot))

        val snapshotId = intent.getStringExtra(IntentCanst.SNAPSHOT_ID)
        getSnapshotDetail(snapshotId ?: "")

        snapshotGoGoodsDetail.setOnClickListener(this)

        adapter = PromotionAdapter(cxTags)
        promotionRv.adapter = adapter
        promotionRv.layoutManager = WrapLinearLayoutManager(this@TradingSnapshotDetailActivity)
        promotionRv.isEnabled = false
        promotionRv.isNestedScrollingEnabled = false
        (promotionRv.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
    }

    /**
     * 获取交易快照详情数据
     */
    private fun getSnapshotDetail(snapshotId: String) {
        showProgress()
        val requestParams = RequestParams()
        requestParams.put("id", snapshotId)
        HttpManager.getInstance()
            .post(AppNetConfig.TRADING_SNAPSHOT_DETAIL, requestParams,
                object : BaseResponse<TradingSnapshotDetail>() {
                    override fun onSuccess(
                        content: String?,
                        baseBean: BaseBean<TradingSnapshotDetail>?,
                        detail: TradingSnapshotDetail?
                    ) {
                        dismissProgress()
                        if (baseBean != null && baseBean.isSuccess && detail != null) {
                            showView(detail)
                        } else {

                        }
                    }

                })
    }

    @SuppressLint("SetTextI18n", "CheckResult")
    private fun showView(detail: TradingSnapshotDetail) {
        //商品id
        skuId = detail.skuId.toString()

        //商品图片or视频
        setBanner(detail.imagesVideosList)

        //商品名 标签
        bindNameTag(detail)
        if (!TextUtils.isEmpty(detail.orderNo)) {
            tvOrderNo.visibility = View.VISIBLE
            tvOrderNo.text = "订单编号：" + detail.orderNo
        } else {
            tvOrderNo.visibility = View.GONE
        }
        if (TextUtils.isEmpty(detail.tracingCode) || !detail.tracingCode.equals("1")) {
            modifyVisibility(View.GONE, tv_layout_traceable_tips, tv_traceable_tips)
        }

        //药品类型及独家
        if (!detail.agent.isNullOrEmpty() && with(detail) { agent?.toInt() } != 1) {
            TagView.visibility = View.GONE
        } else {
            agentTv.visibility = View.VISIBLE
        }

        //促销
        if (detail.cxTagList == null || detail.cxTagList.isEmpty()) {
            promotionTv.visibility = View.INVISIBLE
            promotionRv.visibility = View.INVISIBLE
        } else {
            cxTags.addAll(detail.cxTagList)
            adapter.notifyDataSetChanged()
        }
        // 说明书
        //近效期&远效期
        if (detail.nearEffect.isNullOrEmpty() && detail.farEffect.isNullOrEmpty()) {
            modifyVisibility(View.GONE, validityTitleTv, validityTv)
        } else {
            val nearEffect = detail.nearEffect?.replace("-", ".")
            val farEffect = detail.farEffect?.replace("-", ".")
            validityTv.text = when {
                nearEffect.isNullOrEmpty() && farEffect.isNullOrEmpty() -> ""
                nearEffect.isNullOrEmpty() -> farEffect
                farEffect.isNullOrEmpty() -> nearEffect
                else -> "$nearEffect/$farEffect"
            }
        }
        //中包装
        casingTv.text = "${detail.mediumPackageNum}${detail.productUnit}"
        //拆零
        if (!detail.isSplit.isNullOrEmpty()) {
            when (with(detail) { isSplit?.toInt() }) {
                0 -> {
                    splitTv.visibility = View.VISIBLE
                    splitTv.text = "不可拆零"
                }

                else -> {
                    splitTv.visibility = View.GONE
                }
            }
        } else {
            splitTv.visibility = View.GONE
        }

        //药品名称
        if (detail.showName.isNullOrEmpty()) {
            modifyVisibility(View.GONE, nameTitleTv, nameTv)
        } else {
            nameTv.text = detail.showName
        }
        //规格型号
        if (detail.spec.isNullOrEmpty()) {
            modifyVisibility(View.GONE, specTitleTv, specTv)
        } else {
            specTv.text = detail.spec
        }
        //批准文号
        if (detail.approvalNumber.isNullOrEmpty()) {
            modifyVisibility(View.GONE, approvalNumberTitleTv, approvalNumberTv)
        } else {
            approvalNumberTv.text = detail.approvalNumber
            if ("100005" == detail.categoryFirstId) {
                modifyVisibility(View.GONE, approvalNumberTitleTv, approvalNumberTv)
                approvalNumberTv2.text = "医疗器械注册证或备案凭证编号: " + detail.approvalNumber
                approvalNumberTv2.visibility = View.VISIBLE
            }
        }
        //生产企业
        if (detail.manufacturer.isNullOrEmpty()) {
            modifyVisibility(View.GONE, manufacturerTitleTV, manufacturerTV)
        } else {
            manufacturerTV.text = detail.manufacturer
        }
        //存储条件
        if (detail.storageCondition.isNullOrEmpty()) {
            modifyVisibility(View.GONE, storageConditionTitleTv, storageConditionTv)
        } else {
            storageConditionTv.text = detail.storageCondition
        }
        //处方分类
        if (!detail.drugClassification.isNullOrEmpty()) {
            when (with(detail) { drugClassification?.toInt() }) {
                1 -> drugClassificationTv.text = "甲类OTC"
                2 -> drugClassificationTv.text = "乙类OTC"
                3 -> drugClassificationTv.text = "RX处方药"
                else -> {
                    modifyVisibility(View.GONE, drugClassificationTitleTv, drugClassificationTv)
                }
            }
        } else {
            modifyVisibility(View.GONE, drugClassificationTitleTv, drugClassificationTv)
        }
        //有效期
        if (detail.shelfLife.isNullOrEmpty()) {
            modifyVisibility(View.GONE, shelfLifeTitleTv, shelfLifeTv)
        } else {
            shelfLifeTv.text = detail.shelfLife
        }
        //适应症/功能主治
        if (detail.indication.isNullOrEmpty()) {
            indicationTitleTv.visibility = View.GONE
            indicationTv.visibility = View.GONE
        } else {
            indicationTitleTv.visibility = View.VISIBLE
            indicationTv.visibility = View.VISIBLE
            indicationTv.text = detail.indication
        }
        //温馨提示(tvLayoutWarmPrompt)and近效期、临期商品不退换提示语位置优化
        tv_tips.visibility =
            (if (detail.isActivityType() || detail.isNearEffectiveFlag()) View.VISIBLE else View.GONE)
        if (detail.isActivityType()) {
            tv_tips.text = getString(R.string.tips_optimize_product_detail)
        }
        if (detail.isNearEffectiveFlag()) {
            tv_tips.text = getString(R.string.detail_tv_warm_prompt)
        }
    }

    /**
     * 绑定化OTC,RX标签
     */
    private fun bindNameTag(detail: TradingSnapshotDetail) {
        val list = ArrayList<Drawable>()
        if (!detail.drugClassification.isNullOrEmpty() && with(detail) { drugClassification?.toInt() } in 1..3) {
            val imageUrl = detail.drugClassificationImage!!.makeImageUrl()
            if (!TextUtils.isEmpty(detail.showName)) snapshotDetailGoodsNameTv.text = detail.showName
            ImageHelper.with(this@TradingSnapshotDetailActivity).load(imageUrl)
                .placeholder(R.drawable.jiazaitu_min)
                .diskCacheStrategy(DiskCacheStrategy.SOURCE)
                .dontAnimate().dontTransform().into(object : SimpleTarget<GlideDrawable>() {
                    override fun onResourceReady(
                        resource: GlideDrawable,
                        glideAnimation: GlideAnimation<in GlideDrawable>
                    ) {
                        list.add(resource)
                        initAgent(detail, list)
                    }
                })
        } else {
            initAgent(detail, list)
        }
    }

    /**
     * 设置独家标签
     */
    private fun initAgent(detail: TradingSnapshotDetail, list: ArrayList<Drawable>) {
        if (!detail.isThirdCompany.isNullOrEmpty() && with(detail) { isThirdCompany?.toInt() } == 0) {
            list.add(resources.getDrawable(R.drawable.icon_autotrophy))
        }
        showNameTag(detail.showName.toString(), list)
    }

    /**
     * 设置商品名及标签
     */
    private fun showNameTag(showName: String, list: List<Drawable>) {
        val shopName = getShopNameIcon(showName, list)
        if (!TextUtils.isEmpty(shopName)) snapshotDetailGoodsNameTv.text = shopName
    }

    /**
     * 图标
     */
    private fun getShopNameIcon(shopName: String, icons: List<Drawable>?): SpannableStringBuilder? {
        if (icons != null && icons.isNotEmpty()) {
            val spannableString = SpannableStringBuilder(shopName)
            for (i in icons.indices) {
                val drawable = icons[i]
                //适配图标大小问题
                drawable.setBounds(0, 0, UiUtils.dp2px(35), UiUtils.dp2px(18))
                val centerAlignImageSpan =
                    CenterAlignImageSpan(this@TradingSnapshotDetailActivity, drawable, 4, 2)
                //占个位置
                spannableString.insert(0, "-")
                spannableString.setSpan(
                    centerAlignImageSpan,
                    0,
                    1,
                    Spanned.SPAN_INCLUSIVE_EXCLUSIVE
                )
            }
            return spannableString
        }
        return null
    }

    /**
     * 商品照片
     */
    private fun setBanner(imagesVideosList: List<ImagesVideos>?) {
        var isFlag = false
        val imagesVideos = ArrayList<ImagesVideosListBean>()
        if (imagesVideosList != null && imagesVideosList.isNotEmpty()) {
            for (element in imagesVideosList) {
                val videoListBean = ImagesVideosListBean()
                if (!element.type.isNullOrEmpty() && with(element) { type?.toInt() } == 2) {
                    isFlag = true
                    if (!element.videoUrl.isNullOrEmpty()) {
                        videoListBean.imageUrl = element.videoUrl
                        videoListBean.videoUrl = element.videoUrl
                    }
                } else {
                    videoListBean.imageUrl = element.imageUrl
                }
                videoListBean.type = element.type!!.toInt()
                imagesVideos.add(videoListBean)
            }
            snapshotBannerView.setItemDataInVideo(imagesVideos)
            if (isFlag) {
                videoPlayIv.visibility = View.VISIBLE
            } else {
                videoPlayIv.visibility = View.GONE
            }
            snapshotBannerView.setOnPageChangeListener { position, isFlag ->
                run {
                    if (isFlag) {
                        if (position == 0) {
                            videoPlayIv.visibility = View.VISIBLE
                        } else {
                            videoPlayIv.visibility = View.GONE
                        }
                    } else {
                        videoPlayIv.visibility = View.GONE
                    }
                }
            }
        }
    }

    override fun onClick(v: View?) {
        val intent = Intent(this@TradingSnapshotDetailActivity, ProductDetailActivity::class.java)
        intent.putExtra(IntentCanst.PRODUCTID, skuId)
        startActivity(intent)
    }
}