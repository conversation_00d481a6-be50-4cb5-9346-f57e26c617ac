package com.ybmmarket20.business.snapshot.adapter

import android.annotation.SuppressLint
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.RecyclerView
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.content.ContextCompat
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.GlideDrawable
import com.bumptech.glide.request.animation.GlideAnimation
import com.bumptech.glide.request.target.SimpleTarget
import com.ybm.app.common.ImageLoader.ImageHelper
import com.ybmmarket20.R
import com.ybmmarket20.bean.CxTag
import com.ybmmarket20.business.snapshot.ext.makeImageUrl
import com.ybmmarket20.utils.ImageUtil
import com.ybmmarket20.utils.UiUtils

/* 
 * Created by sean on 2019-08-09
 */

class PromotionAdapter(private val promotionList: List<CxTag>) : RecyclerView.Adapter<PromotionAdapter.ViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_trading_promotion, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount() = promotionList.size


    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.content.text = promotionList[position].description
        applyTag(holder, position)
    }

    @SuppressLint("ResourceAsColor")
    private fun applyTag(holder: ViewHolder, position: Int) {
        val params = ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        params.setMargins(0, 0, 0, 0)
        holder.tag.gravity = Gravity.CENTER
        holder.tag.setSingleLine(true)
        holder.tag.layoutParams = params
        holder.tag.setPadding(UiUtils.dp2px(4), UiUtils.dp2px(1), UiUtils.dp2px(4), UiUtils.dp2px(1))
        holder.tag.setTextSize(TypedValue.COMPLEX_UNIT_SP, 10f)
        val drawRes: Int
        val colorRes: Int
        when (promotionList[position].uiType?.toInt()) {
            //标签显示类型 1,临期 2，券 3，自定义1 4，自定义2
            1 -> {
                drawRes = R.drawable.bg_brand_item_type1
                colorRes = R.color.white
            }
            2 -> {
                drawRes = R.drawable.shape_trading_snapshot_promotion
                colorRes = R.color.trading_snapshot_tag_uitype_2
            }
            3 -> {
                drawRes = R.drawable.bg_brand_item_type3
                colorRes = R.color.brand_icon_type3
            }
            5//医保
            -> {
                drawRes = R.drawable.bg_brand_item_health_insurance
                colorRes = R.color.white
            }
            4 -> {
                drawRes = R.drawable.bg_brand_item_type4
                colorRes = R.color.brand_icon_type4
            }
            999 ->{
                holder.tag.visibility = View.GONE
                holder.ivIconType.visibility = View.VISIBLE
                val imageUrl = promotionList[position].name!!.makeImageUrl()
                ImageUtil.load(holder.ivIconType.context,imageUrl,holder.ivIconType)

                val params = ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT)
                params.leftToRight = R.id.iv_icon_type_01
                params.topToTop = holder.itemView.id
                params.bottomToBottom = holder.itemView.id
                holder.content.layoutParams = params
                //
                drawRes = R.drawable.bg_brand_item_type4
                colorRes = R.color.brand_icon_type4
            }

            else -> {
                drawRes = R.drawable.bg_brand_item_type4
                colorRes = R.color.brand_icon_type4
            }
        }
        if (holder.tag.visibility == View.VISIBLE){
            holder.tag.setBackgroundResource(drawRes)
            holder.tag.setTextColor(ContextCompat.getColor(holder.itemView.context, colorRes))
            holder.tag.text = promotionList[position].name
        }
    }


    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

        val ivIconType: ImageView = itemView.findViewById(R.id.iv_icon_type_01)
        val tag: AppCompatTextView = itemView.findViewById(R.id.tag)
        val content: AppCompatTextView = itemView.findViewById(R.id.content)
    }
}