package com.ybmmarket20.business.snapshot.entity

/*
 * Created by sean on 2019-08-05
 */

import com.ybm.app.bean.AbstractMutiItemEntity
import com.ybmmarket20.bean.ProductActivityTag
import com.ybmmarket20.bean.Tag
import java.io.Serializable

class SnapshotItemModel : AbstractMutiItemEntity(),Serializable {

    var balanceFlag: Int = 0
    var balancePercent: Double = 0.toDouble()
    var branchCode: String? = null
    var id: Int = 0
    var imageUrl: String? = null
    var orderNo: String? = null
    var productActivityTag: ProductActivityTag? = null
    var productAmount: Int = 0
    var productName: String? = null
    var productPrice: Double = 0.toDouble()
    var skuId: Int = 0
    var spec: String? = null
    var subTotal: Double = 0.toDouble()
    var tagJsonStr: String? = null
    var tagList: List<Tag>? = null
    var packageCount: Int = 0
    var packageId: Int = 0
    var preferentialAmount: Double = 0.toDouble()
    var preferentialCount: Int = 0
    var subtotalPrice: Double = 0.toDouble()
    var totalPrice: Double = 0.toDouble()
    /*------------------渠道-------------------*/
    var channelCode: String? = null//渠道编码：默认为 1; 药帮忙 1; 2 宜块钱
    var isThirdCompany: Int = 0//是否是自营（0：是；1：否）

    var nearEffect: String?=""//近效期
    var farEffect: String?=""//远效期

    fun isIsThirdCompany(): Boolean {
        return isThirdCompany == 0
    }

    override fun getItemType(): Int {
        if (itemType <= 0) {
            itemType = ITEMTYPE_CONTENT
        }
        return super.getItemType()
    }

    companion object {

        val ITEMTYPE_PACKAGE_HEADER = 1
        val ITEMTYPE_PACKAGE_CONTENT = 2
        val ITEMTYPE_PACKAGE_FOOTER = 3
        val ITEMTYPE_CONTENT = 4
    }
}
