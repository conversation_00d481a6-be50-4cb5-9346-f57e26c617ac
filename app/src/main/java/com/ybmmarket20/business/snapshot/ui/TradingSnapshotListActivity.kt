package com.ybmmarket20.business.snapshot.ui

import android.text.TextUtils
import android.view.View
import androidx.recyclerview.widget.SimpleItemAnimator
import com.github.mzule.activityrouter.annotation.Router
import com.ybm.app.bean.NetError
import com.ybm.app.view.WrapLinearLayoutManager
import com.ybmmarket20.R
import com.ybmmarket20.adapter.TradingSnapshotListAdapter
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.Detail
import com.ybmmarket20.bean.Packages
import com.ybmmarket20.bean.TradingSnapshotListBean
import com.ybmmarket20.business.snapshot.entity.SnapshotItemModel
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.showPayTipsPop
import com.ybmmarket20.view.DividerLine
import kotlinx.android.synthetic.main.activity_trading_snapshot.conTips
import kotlinx.android.synthetic.main.activity_trading_snapshot.goodsNumTv
import kotlinx.android.synthetic.main.activity_trading_snapshot.recyclerview_goods
import kotlinx.android.synthetic.main.activity_trading_snapshot.tvMore
import kotlinx.android.synthetic.main.activity_trading_snapshot.tvTipsContent
import kotlinx.android.synthetic.main.activity_trading_snapshot.tvTipsTitle

/**
 * Created by sean on 2019/07/24.
 * 交易快照列表页
 */
@Router("tradingSnapshotList", "tradingSnapshotList/:order_no")
class TradingSnapshotListActivity : BaseActivity() {

    private var snapshotItemList: ArrayList<SnapshotItemModel> = ArrayList()
    private lateinit var adapter: TradingSnapshotListAdapter
    private var popContent = ""

    override fun getContentViewId(): Int = R.layout.activity_trading_snapshot


    override fun initData() {
        setTitle(resources.getString(R.string.trading_snapshot))

        val orderNo = intent.getStringExtra(IntentCanst.ORDER_NO)
        adapter = TradingSnapshotListAdapter(snapshotItemList)
        recyclerview_goods.adapter = adapter
        recyclerview_goods.layoutManager = WrapLinearLayoutManager(this@TradingSnapshotListActivity)
        recyclerview_goods.addItemDecoration(DividerLine(DividerLine.VERTICAL).apply {
            this.setSize(1)
            this.setColor(-0x1e1e1f)
        })
        recyclerview_goods.isEnabled = false
        recyclerview_goods.isNestedScrollingEnabled = false
        (recyclerview_goods.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false

        getTradingSnapshotDetail(orderNo ?: "")
        tvMore.setOnClickListener {
            showPayTipsPop(this, popContent, "确定", false, { })
        }
    }

    /**
     * 获取交易快照列表数据
     * @param orderNo 订单编号
     */
    private fun getTradingSnapshotDetail(orderNo: String) {
        showProgress()
        val requestParams = RequestParams()
        requestParams.put("orderNo", orderNo)
        HttpManager.getInstance()
            .post(AppNetConfig.TRADING_SNAPSHOT_LIST, requestParams,
                object : BaseResponse<TradingSnapshotListBean>() {

                    override fun onSuccess(
                        content: String?,
                        listBean: BaseBean<TradingSnapshotListBean>?,
                        data: TradingSnapshotListBean?
                    ) {
                        dismissProgress()

                        if (listBean != null && listBean.isSuccess && data != null) {
                            val tradingSnapshotListBean = listBean.data
                            setGoodsNum(tradingSnapshotListBean.num)
                            setGoodsTips(tradingSnapshotListBean)
                            val convertData = convertData(tradingSnapshotListBean)
                            adapter.setNewData(convertData)
                        }
                    }

                    override fun onFailure(error: NetError?) {
                        super.onFailure(error)
                    }
                })
    }

    private fun setGoodsNum(num: Int) {
        goodsNumTv.text =
            String.format(resources.getString(R.string.trading_snapshot_order_goods_num), num)
    }

    private fun setGoodsTips(bean: TradingSnapshotListBean) {
        if (!TextUtils.isEmpty(bean.showStatement) && bean.showStatement.equals("1")) {
            conTips.visibility = View.VISIBLE
        } else {
            conTips.visibility = View.GONE
        }
        if (!TextUtils.isEmpty(bean.title)) {
            tvTipsTitle.text = bean.title
        }
        if (!TextUtils.isEmpty(bean.contentOne)) {
            tvTipsContent.text = bean.contentOne
        }
        if (!TextUtils.isEmpty(bean.contentTwo)) {
            popContent = bean.contentTwo.toString()
        }

    }

    private fun convertData(source: TradingSnapshotListBean): List<SnapshotItemModel> {
        val detailList = source.detailList
        val packageList = source.packageList

        snapshotItemList = ArrayList<SnapshotItemModel>()

        if (packageList!!.isNotEmpty()) {
            for ((index) in packageList.withIndex()) {
                addPackageHeader(packageList[index], SnapshotItemModel.ITEMTYPE_PACKAGE_HEADER)
                if (packageList[index].packageDetailList.isNotEmpty()) {
                    for ((indices) in packageList[index].packageDetailList.withIndex()) {
                        addProduct(
                            packageList[index].packageDetailList[indices],
                            SnapshotItemModel.ITEMTYPE_PACKAGE_CONTENT
                        )
                    }
                }
                addPackageFooter(packageList[index], SnapshotItemModel.ITEMTYPE_PACKAGE_FOOTER)
            }
        }

        if (detailList!!.isNotEmpty()) {
            for ((index) in detailList.withIndex()) {
                addProduct(detailList[index], SnapshotItemModel.ITEMTYPE_CONTENT)
            }
        }
        return snapshotItemList
    }

    /**
     * 添加套餐footer
     */
    private fun addPackageFooter(packages: Packages, itemType: Int) {
        val itemModel = SnapshotItemModel()
        itemModel.itemType = itemType
        itemModel.subtotalPrice = packages.subtotalPrice
        itemModel.totalPrice = packages.totalPrice
        snapshotItemList.add(itemModel)
    }

    /**
     * 添加套餐header
     */
    private fun addPackageHeader(packagesList: Packages, itemType: Int) {
        val itemModel = SnapshotItemModel()
        itemModel.itemType = itemType
        itemModel.productName = "搭配套餐"
        itemModel.packageCount = packagesList.packageCount
        itemModel.packageId = packagesList.packageId
        snapshotItemList.add(itemModel)
    }

    /**
     * 添加套餐中详细商品
     */
    private fun addProduct(detail: Detail, itemType: Int) {
        val itemModel = SnapshotItemModel()
        itemModel.itemType = itemType
        itemModel.imageUrl = detail.imageUrl
        itemModel.skuId = detail.skuId
        itemModel.productName = detail.productName
        itemModel.productPrice = detail.productPrice
        itemModel.productAmount = detail.productAmount
        itemModel.spec = detail.spec
        itemModel.subTotal = detail.subTotal
        itemModel.branchCode = detail.branchCode
        itemModel.id = detail.id
        itemModel.orderNo = detail.orderNo
        itemModel.productActivityTag = detail.productActivityTag
        itemModel.tagList = detail.tagList
        itemModel.channelCode = detail.channelCode
        itemModel.isThirdCompany = detail.isThirdCompany
        itemModel.farEffect = detail.farEffect
        itemModel.nearEffect = detail.nearEffect
        snapshotItemList.add(itemModel)
    }


}