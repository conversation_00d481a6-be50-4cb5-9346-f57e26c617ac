package com.ybmmarket20.business.correction.filter

/*
 * Created by sean on 2019-08-12
 */

import android.text.InputFilter
import android.text.Spanned
import android.text.TextUtils
import java.util.regex.Pattern

class EditInputFilter : InputFilter {
    internal var p: Pattern = Pattern.compile("([0-9]|\\.)*")

    override fun filter(source: CharSequence, start: Int, end: Int, dest: Spanned, dstart: Int, dend: Int): CharSequence {
        val sourceText = source.toString()
        val destText = dest.toString()
        if (TextUtils.isEmpty(sourceText)) {
            return if (dstart == 0 && destText.indexOf(POINTER) == 1) {//保证小数点不在第一个位置
                "0"
            } else ""
        }
        val matcher = p.matcher(source)

        if (destText.contains(POINTER)) {
            if (!matcher.matches()) {
                return ""
            } else {
                if (POINTER == source) { //只能输入一个小数点
                    return ""
                }
            }
            //验证小数点精度，保证小数点后只能输入两位
            val index = destText.indexOf(POINTER)
            val length = destText.trim { it <= ' ' }.length - index
            if (length > POINTER_LENGTH && dstart > index) {
                return ""
            }
        } else {
            //没有输入小数点的情况下，只能输入小数点和数字，但首位不能输入小数点和0
            if (!matcher.matches()) {
                return ""
            } else {
                if (POINTER == source && dstart == 0) {//第一个位置输入小数点的情况
                    return "0."
                } else if ("0" == source && dstart == 0) {
                    //用于修复能输入多位0
                    return ""
                }
            }
        }

        val first = destText.substring(0, dstart)
        val second = destText.substring(dstart)
        val sum = first + sourceText + second

        //验证输入金额的大小
        val sumText = java.lang.Double.parseDouble(sum)

        return if (sumText > MAX_VALUE) {
            dest.subSequence(dstart, dend)
        } else dest.subSequence(dstart, dend).toString() + sourceText

    }

    companion object {

        const val POINTER_LENGTH = 2
        private const val POINTER = "."
        const val MAX_VALUE = 100000000
    }
}
