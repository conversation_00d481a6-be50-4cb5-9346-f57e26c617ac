package com.ybmmarket20.business.correction.tools

/*
 * Created by sean on 2019-08-12
 */

object GoodsCorrectionReasonUtil {

    const val json = "{\n" +
            "\n" +
            "\t\"data\": [{\n" +
            "\t\t\"name\": \"图片有误\",\n" +
            "\t\t\"id\": 0,\n" +
            "\"isChecked\":false\n" +
            "\t}, {\n" +
            "\t\t\"name\": \"规格有误\",\n" +
            "\t\t\"id\": 1,\n" +
            "\"isChecked\":false\n" +
            "\t}, {\n" +
            "\t\t\"name\": \"生产厂家有误\",\n" +
            "\t\t\"id\": 2,\n" +
            "\"isChecked\":false\n" +
            "\t}, {\n" +
            "\t\t\"name\": \"中包装有误\",\n" +
            "\t\t\"id\": 3,\n" +
            "\"isChecked\":false\n" +
            "\t}, {\n" +
            "\t\t\"name\": \"处方类型有误\",\n" +
            "\t\t\"id\": 4,\n" +
            "\"isChecked\":false\n" +
            "\t}, {\n" +
            "\t\t\"name\": \"批准文号有误\",\n" +
            "\t\t\"id\": 5,\n" +
            "\"isChecked\":false\n" +
            "\t}, {\n" +
            "\t\t\"name\": \"说明书有误\",\n" +
            "\t\t\"id\": 6,\n" +
            "\"isChecked\":false\n" +
            "\t}, {\n" +
            "\t\t\"name\": \"其他\",\n" +
            "\t\t\"id\": 7,\n" +
            "\"isChecked\":false\n" +
            "\t}]\n" +
            "}"

}
