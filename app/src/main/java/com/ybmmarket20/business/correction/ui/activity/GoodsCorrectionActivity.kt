package com.ybmmarket20.business.correction.ui.activity

import android.text.SpannableString
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.GoodsCorrection
import com.ybmmarket20.business.correction.adapter.CorrectionGoodsReasonAdapter
import com.ybmmarket20.bean.Lable
import com.ybmmarket20.business.correction.ext.span
import com.ybmmarket20.business.correction.tools.dp2px
import com.ybmmarket20.business.correction.ui.fragment.CommonCorrectionFragment
import com.ybmmarket20.business.correction.widget.FlowLayoutManager
import com.ybmmarket20.business.correction.widget.SoftKeyInputHidWidget
import com.ybmmarket20.business.correction.widget.SpaceItemDecoration
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.SpUtil
import kotlinx.android.synthetic.main.activity_correction_goods.*
import java.io.File

/* 
 * Created by sean on 2019-08-20
 */

class GoodsCorrectionActivity : BaseCorrectionActivity(), CorrectionGoodsReasonAdapter.CheckChangeListener {

    private lateinit var commonFragment: CommonCorrectionFragment
    private lateinit var skuId: String
    private lateinit var adapter: CorrectionGoodsReasonAdapter

    override fun getContentViewId(): Int = R.layout.activity_correction_goods

    override fun initData() {

        setTitle(getString(R.string.correction_goods))
        skuId = intent.getStringExtra(IntentCanst.SKU_ID)?: ""
        lableTips.text = SpannableString(lableTips.text.toString()).span()
        initCommonFragment()
        initReasonComponent()
        SoftKeyInputHidWidget.assistActivity(this)
    }


    /**
     * 初始化公共业务Fragment
     */
    private fun initCommonFragment() {
        commonFragment = supportFragmentManager.findFragmentById(R.id.commonCorrection) as CommonCorrectionFragment
        commonFragment.setFrom(CommonCorrectionFragment.From.GOODS)
        commonFragment.forcedParamsTitle(true)
    }

    /**
     * 初始化商品信息纠错原因部分
     */

    private fun initReasonComponent() {
        adapter = CorrectionGoodsReasonAdapter()
        val layoutManager = FlowLayoutManager()
        val itemDecoration = SpaceItemDecoration(dp2px(5f))
        lableRv.layoutManager = layoutManager
        lableRv.addItemDecoration(itemDecoration)
        lableRv.adapter = adapter
        adapter.setCheckChangeListener(this)
    }

    /**
     * 原因选中监听
     */
    override fun onCheckChange(bean: Lable.DataBean) {
        commonFragment.submitEnable(adapter.checkedReason.isNotEmpty() && commonFragment.isForcedParamsDone())
    }

    fun isReasonDone() = adapter.checkedReason.isNotEmpty()

    /**
     * 上传数据
     */
    override fun uploadInfo() {
        val builder = StringBuilder()
        val checkedReason = adapter.checkedReason
        for (integer in checkedReason) {
            builder.append(integer).append(",")
        }
        val productContentStr = builder.toString()
        val index = productContentStr.lastIndexOf(",")
        val productContent = productContentStr.substring(0, index)

        showProgress()
        val requestParams = RequestParams()
        requestParams.put("merchantId", SpUtil.getMerchantid())
        requestParams.put("skuId", skuId)
        requestParams.put("type", "2")
        requestParams.put("productContent", productContent)
        val selectList = commonFragment.getImages()
        if (selectList.size > 0) {
            for (i in selectList.indices) {
                val path: String
                val compressed = selectList.get(i).isCompressed
                path = if (compressed) {
                    selectList[i].compressPath
                } else {
                    selectList[i].path
                }
                val file = File(path)
                if (file.exists() && file.length() > 0) {
                    requestParams.put("fileImgs$i", file)
                }
            }
        }
        requestParams.put("supplementNote", commonFragment.getInstruction())
        HttpManager.getInstance().post(AppNetConfig.GOODS_MISTAKE_CORRECTION, requestParams, object : BaseResponse<GoodsCorrection>() {

            override fun onSuccess(content: String, obj: BaseBean<GoodsCorrection>?, goodsCorrection: GoodsCorrection) {
                dismissProgress()
                if (obj != null && obj.isSuccess) {
                    ToastUtils.showShort(goodsCorrection.msg)
                    finish()
                } else {
                    ToastUtils.showShort(goodsCorrection.msg)
                }
                finish()
            }

            override fun onFailure(error: NetError) {
                dismissProgress()
            }
        })
    }

}