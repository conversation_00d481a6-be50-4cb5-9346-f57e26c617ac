package com.ybmmarket20.business.correction.ui.activity

import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.GoodsCorrection
import com.ybmmarket20.business.correction.ui.fragment.CommonCorrectionFragment
import com.ybmmarket20.business.correction.widget.SoftKeyInputHidWidget
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.SpUtil
import java.io.File

/* 
 * Created by sean on 2019-08-20
 */

class OthersCorrectionActivity : BaseCorrectionActivity() {

    private lateinit var commonFragment: CommonCorrectionFragment

    private lateinit var skuId: String

    override fun getContentViewId(): Int = R.layout.activity_correction_others

    override fun initData() {

        setTitle(getString(R.string.correction_others))
        initCommonFragment()
        skuId = intent.getStringExtra(IntentCanst.SKU_ID)?: ""
        SoftKeyInputHidWidget.assistActivity(this)
    }

    /**
     * 初始化公共业务Fragment
     */
    private fun initCommonFragment() {
        commonFragment = supportFragmentManager.findFragmentById(R.id.commonCorrection) as CommonCorrectionFragment
        commonFragment.setFrom(CommonCorrectionFragment.From.OTHERS)
        commonFragment.forcedParamsTitle(true)
    }

    override fun uploadInfo() {
        showProgress()
        val requestParams = RequestParams()
        requestParams.put("merchantId", SpUtil.getMerchantid())
        requestParams.put("skuId", skuId)
        requestParams.put("type", "3")
        val selectList = commonFragment.getImages()
        if (selectList.size > 0) {
            for (i in selectList.indices) {
                val compressed = selectList[i].isCompressed
                val path = if (compressed) {
                    selectList[i].compressPath
                } else {
                    selectList[i].path
                }
                val file = File(path)
                if (file.exists() && file.length() > 0) {
                    requestParams.put("fileImgs$i", file)
                }
            }
        }
        requestParams.put("supplementNote", commonFragment.getInstruction())
        HttpManager.getInstance().post(AppNetConfig.GOODS_MISTAKE_CORRECTION, requestParams, object : BaseResponse<GoodsCorrection>() {

            override fun onSuccess(content: String, obj: BaseBean<GoodsCorrection>?, goodsCorrection: GoodsCorrection) {
                dismissProgress()
                if (obj != null && obj.isSuccess) {
                    ToastUtils.showShort(goodsCorrection.msg)
                    finish()
                } else {
                    ToastUtils.showShort(goodsCorrection.msg)
                }
                finish()
            }

            override fun onFailure(error: NetError) {
                dismissProgress()
            }
        })
    }
}