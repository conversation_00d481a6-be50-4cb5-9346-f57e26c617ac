package com.ybmmarket20.business.correction.adapter;
/*
 * Created by sean on 2019-08-12
 */

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;

import com.google.gson.Gson;
import com.ybmmarket20.R;
import com.ybmmarket20.bean.Lable;
import com.ybmmarket20.business.correction.tools.GoodsCorrectionReasonUtil;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class CorrectionGoodsReasonAdapter extends RecyclerView.Adapter<CorrectionGoodsReasonAdapter.ViewHolder> {


    private List<Lable.DataBean> lableData;
    private Set<Integer> checkReasonSet = new HashSet<>();

    public interface CheckChangeListener {

        void onCheckChange(Lable.DataBean bean);

    }

    private CorrectionGoodsReasonAdapter.CheckChangeListener listener;

    public void setCheckChangeListener(CorrectionGoodsReasonAdapter.CheckChangeListener listener) {
        this.listener = listener;
    }


    public CorrectionGoodsReasonAdapter() {
        converData();
    }

    private void converData() {
        Gson gson = new Gson();
        Lable lable = gson.fromJson(GoodsCorrectionReasonUtil.json, Lable.class);
        lableData = lable.getData();
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_correction_label, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        holder.cb.setOnCheckedChangeListener(null);
        holder.cb.setChecked(lableData.get(position).isChecked());
        holder.cb.setText(lableData.get(position).getName());
        holder.cb.setOnCheckedChangeListener((buttonView, isChecked) -> {
            int id = lableData.get(position).getId();
            lableData.get(position).setChecked(isChecked);
            if (isChecked) {
                checkReasonSet.add(id);
            } else {
                checkReasonSet.remove(id);
            }
            listener.onCheckChange(lableData.get(position));
        });
    }

    @Override
    public int getItemCount() {
        return lableData.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {

        private CheckBox cb;

        public ViewHolder(View itemView) {
            super(itemView);
            cb = itemView.findViewById(R.id.cb);
        }

//        public void bindData(Lable.DataBean info) {
//            cb.setText(info.getName());
//            cb.setChecked(info.isChecked());
//            cb.setOnClickListener(view -> {
//                info.setChecked(cb.isChecked());
//                if (listener != null) {
//                    listener.onCheckChange(info);
//                }
//            });
//
//            cb.setCompoundDrawables(null, null, null, null);
//        }
    }


    public Set<Integer> getCheckedReason() {
        return checkReasonSet;
    }


}
