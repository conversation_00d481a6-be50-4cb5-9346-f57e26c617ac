package com.ybmmarket20.business.correction.ui.activity

import android.text.Editable
import android.text.InputFilter
import android.text.SpannableString
import android.text.TextUtils
import android.view.View
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.GoodsCorrection
import com.ybmmarket20.business.correction.ext.span
import com.ybmmarket20.business.correction.filter.EditInputFilter
import com.ybmmarket20.business.correction.tools.modifyVisibility
import com.ybmmarket20.business.correction.ui.fragment.CommonCorrectionFragment
import com.ybmmarket20.business.correction.watcher.CustomTextWatcher
import com.ybmmarket20.business.correction.widget.PriceCorrectionReasonDialog
import com.ybmmarket20.business.correction.widget.SoftKeyInputHidWidget
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.view.TransparentDialog
import kotlinx.android.synthetic.main.activity_correction_price.*
import java.io.File

/* 
 * Created by sean on 2019-08-19
 */

class PriceCorrectionActivity : BaseCorrectionActivity(), View.OnClickListener, CustomTextWatcher,
    TransparentDialog.ItemSelectListener {

    private lateinit var skuPrice: String
    private lateinit var skuId: String
    private lateinit var dialog: PriceCorrectionReasonDialog
    private lateinit var commonFragment: CommonCorrectionFragment
    private var status = false //标记必传参数状态 true 图片+补充说明 false 建议价格+参考平台

    override fun getContentViewId() = R.layout.activity_correction_price

    override fun initData() {

        setTitle(getString(R.string.correction_price))
        skuPrice = intent.getStringExtra(IntentCanst.SKU_PRICE) ?: ""
        skuId = intent.getStringExtra(IntentCanst.SKU_ID) ?: ""
        currentPriceTv.text = skuPrice

        setPriceRange()
        setForcedParamsTitleAndWatcher()
        initCommonFragment()
        dialog = PriceCorrectionReasonDialog(this@PriceCorrectionActivity)
        initListener()
    }

    /**
     * 设置必传参数标题及内容监听
     */
    private fun setForcedParamsTitleAndWatcher() {

        correctionReasonTipsTv.text =
            SpannableString(resources.getString(R.string.correction_reason)).span()
        suggestedPriceTipsTv.text =
            SpannableString(resources.getString(R.string.correction_suggest_price)).span()
        platformNameTipsTv.text =
            SpannableString(resources.getString(R.string.correction_refer_platform)).span()

        discountsEt.addTextChangedListener(this)
        platformNameEt.addTextChangedListener(this)
    }

    /**
     * 设置价格限制区间
     */
    private fun setPriceRange() {
        discountsEt.filters = arrayOf<InputFilter>(EditInputFilter())
    }

    /**
     * 初始化公共业务Fragment
     */
    private fun initCommonFragment() {
        commonFragment =
            supportFragmentManager.findFragmentById(R.id.commonCorrection) as CommonCorrectionFragment
        commonFragment.setFrom(CommonCorrectionFragment.From.PRICE)
        commonFragment.forcedParamsTitle(false)
    }

    /**
     * 初始化监听器
     */
    private fun initListener() {
        correctionReasonCl.setOnClickListener(this@PriceCorrectionActivity)
        SoftKeyInputHidWidget.assistActivity(this)
    }

    /**
     * 验证EditText必传参数
     */
    fun isForcedParamsDone() =
        !!TextUtils.isEmpty(discountsEt.text) && !!TextUtils.isEmpty(platformNameEt.text)

    /**
     * 获取当前显示状态
     */
    fun getStatus(): Boolean = status

    /**
     * 刷新布局
     * refres 为「true」时 隐藏「建议价格」和「参考平台」 且 必传字段标题变化为「图片上传」和「补充说明」并更改样式
     * refres 为「false」时 显示「建议价格」和「参考平台」 且为必传字段 取消 「图片上传」和「补充说明」样式
     */
    private fun refreshLayout(refresh: Boolean) {
        if (refresh) {
            commonFragment.forcedParamsTitle(refresh)
            modifyVisibility(
                View.GONE,
                suggestedPriceTipsTv,
                discountsEt,
                divider2,
                divider3,
                platformNameTipsTv,
                platformNameEt
            )
        } else {
            commonFragment.forcedParamsTitle(refresh)
            modifyVisibility(
                View.VISIBLE,
                suggestedPriceTipsTv,
                discountsEt,
                divider2,
                divider3,
                platformNameTipsTv,
                platformNameEt
            )
        }
    }


    /**
     * 文字内容变化监听
     */
    override fun afterTextChanged(editable: Editable) {
        if (status) {
            commonFragment.submitEnable(commonFragment.isForcedParamsDone())
        } else {
            commonFragment.submitEnable(isForcedParamsDone())
        }
    }


    /**
     * 点击事件监听
     */
    override fun onClick(view: View) {
        when (view) {
            correctionReasonCl -> {
                dialog.setItemSelectListener(this@PriceCorrectionActivity)
                dialog.show()
            }
        }
    }

    /**
     * 纠错原因选择监听
     * 当选择「其他」时 status 为 true  此时必传参数为 上传图片和补充说明
     * 否则 status 为 false 此时必传参数为 建议价格和参考平台
     */
    override fun itemSelect(itemInfo: TransparentDialog.ItemInfo) {
        correctionReasonTv.text = itemInfo.itemName
        status = itemInfo.itemName == "其他"
        commonFragment.updateFlag(status)
        refreshLayout(status)
        if (status) {
            commonFragment.submitEnable(commonFragment.isForcedParamsDone())
        } else {
            commonFragment.submitEnable(isForcedParamsDone())
        }
    }

    /**
     * 上传数据
     */
    override fun uploadInfo() {
        showProgress()
        val requestParams = RequestParams()
        requestParams.put("merchantId", SpUtil.getMerchantid())
        requestParams.put("skuId", skuId)
        if (!status) {
            requestParams.put("modifyPrice", discountsEt.text.toString())
            requestParams.put("refPlatform", platformNameEt.text.toString())
        }
        requestParams.put("type", "1")
        when {
            correctionReasonTv.text == "售价偏高" -> requestParams.put("priceAdjustType", "1")
            correctionReasonTv.text == "建议零售价有误" -> requestParams.put("priceAdjustType", "2")
            correctionReasonTv.text == "其他" -> requestParams.put("priceAdjustType", "3")
        }
        val selectList = commonFragment.getImages()
        if (selectList.size > 0) {
            for (i in selectList.indices) {
                val path: String
                val compressed = selectList[i].isCompressed
                if (compressed) {
                    path = selectList[i].compressPath
                } else {
                    path = selectList[i].path
                }
                val file = File(path)
                if (file.exists() && file.length() > 0) {
                    requestParams.put("fileImgs$i", file)
                }
            }
        }
        requestParams.put("supplementNote", commonFragment.getInstruction())
        HttpManager.getInstance().post(
            AppNetConfig.GOODS_MISTAKE_CORRECTION,
            requestParams,
            object : BaseResponse<GoodsCorrection>() {

                override fun onSuccess(
                    content: String,
                    obj: BaseBean<GoodsCorrection>?,
                    goodsCorrection: GoodsCorrection
                ) {
                    dismissProgress()
                    if (obj != null && obj.isSuccess) {
                        ToastUtils.showShort(goodsCorrection.msg)
                        finish()
                    } else {
                        ToastUtils.showShort(goodsCorrection.msg)
                    }
                }

                override fun onFailure(error: NetError) {
                    dismissProgress()
                }
            })
    }

}