package com.ybmmarket20.business.correction.ui.activity

import android.content.Context
import android.os.IBinder
import android.view.MotionEvent
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import com.ybmmarket20.common.BaseActivity

/*
 * Created by sean on 2019-08-19
 */

abstract class BaseCorrectionActivity : BaseActivity() {

    /**
     * 处理点击空白区域隐藏软键盘
     */
    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {

        if (ev.action == MotionEvent.ACTION_DOWN) {
            val currentView = currentFocus
            if (isShouldHideKeyboard(currentView, ev)) {
                currentView?.windowToken?.let { hideKeyboard(it) }
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    /**
     * 判断点击位置是否在EditText的外部
     */
    private fun isShouldHideKeyboard(view: View?, ev: MotionEvent): Boolean {
        if (view != null && view is EditText) {
            val location = intArrayOf(0, 0)
            view.getLocationInWindow(location)
            val left = location[0]
            val top = location[1]
            val bottom = top + view.height
            val right = left + view.width
            return !(ev.x > left && ev.x < right && ev.y > top && ev.y < bottom)
        }
        return false
    }

    /**
     * 隐藏软键盘
     */
    private fun hideKeyboard(token: IBinder) {
        val inputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.hideSoftInputFromWindow(token, InputMethodManager.HIDE_NOT_ALWAYS)
    }

    abstract fun uploadInfo()


}
