package com.ybmmarket20.business.correction.ext

import android.text.SpannableString
import android.text.Spanned
import android.text.style.AbsoluteSizeSpan
import android.text.style.ForegroundColorSpan
import com.ybmmarket20.R
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.utils.UiUtils

/* 
 * Created by sean on 2019-08-20
 */

fun SpannableString.span(): SpannableString {
    val colorSpan = ForegroundColorSpan(YBMAppLike.getAppContext().resources.getColor(R.color.correction_require))
    this.setSpan(colorSpan, 0, 1, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
    return this
}

fun SpannableString.span(start: Int, end: Int): SpannableString {
    val colorSpan = ForegroundColorSpan(YBMAppLike.getAppContext().resources.getColor(R.color.correction_require))
    this.setSpan(colorSpan, start, end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
    return this
}

fun SpannableString.priceSpan(): SpannableString {
    val main = AbsoluteSizeSpan(UiUtils.sp2px(16), false)
    this.setSpan(main, 4, length - 3, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
    return this
}