package com.ybmmarket20.business.correction.widget;
/*
 * Created by sean on 2019-09-02
 */

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.text.style.ImageSpan;

import com.ybmmarket20.common.util.ConvertUtils;

public class CenterAlignImageSpan extends ImageSpan {

    private static final int ALIGN_FONTCENTER = 2;
    private int padding;


    public CenterAlignImageSpan(Context context, Drawable drawable, int rightPadding, int verticalAlignment) {
        super(drawable);
        padding = ConvertUtils.dp2px(rightPadding);
    }

    public CenterAlignImageSpan(Context context, Bitmap b, int verticalAlignment) {
        super(context, b, verticalAlignment);
    }


    public CenterAlignImageSpan(Context context, int resourceId, int verticalAlignment) {
        super(context, resourceId, verticalAlignment);
    }

    @Override
    public void draw(<PERSON>vas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom,
                     Paint paint) {

        Drawable drawable = getDrawable();
        Rect rect = new Rect();
        boolean padding = drawable.getPadding(rect);
        canvas.save();

        Paint.FontMetricsInt fm = paint.getFontMetricsInt();

        int transY = bottom - drawable.getBounds().bottom;
        if (mVerticalAlignment == ALIGN_BASELINE) {
            transY -= fm.descent;
        } else if (mVerticalAlignment == ALIGN_FONTCENTER) {
            transY = ((y + fm.descent) + (y + fm.ascent)) / 2 - drawable.getBounds().bottom / 2;
        }
        canvas.translate(x, transY);
        drawable.draw(canvas);
        canvas.restore();
    }


    public int getSize(Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
        Drawable d = getDrawable();
        Rect rect = d.getBounds();
        if (fm != null) {
            Paint.FontMetricsInt fmPaint = paint.getFontMetricsInt();
            int fontHeight = fmPaint.bottom - fmPaint.top;
            int drHeight = rect.bottom - rect.top;

            int top = drHeight / 2 - fontHeight / 4;
            int bottom = drHeight / 2 + fontHeight / 4;

            fm.ascent = -bottom;
            fm.top = -bottom;
            fm.bottom = top;
            fm.descent = top;
        }
        return rect.right+padding;
    }
}