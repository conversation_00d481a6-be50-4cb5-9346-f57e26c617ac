package com.ybmmarket20.business.correction.ui.fragment

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.text.Editable
import android.text.SpannableString
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import android.widget.PopupWindow
import android.widget.TextView
import android.widget.Toast
import androidx.recyclerview.widget.GridLayoutManager
import com.luck.picture.lib.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.PictureMimeType
import com.luck.picture.lib.entity.LocalMedia
import com.tbruyelle.rxpermissions2.RxPermissions
import com.ybm.app.utils.PermissionDialogUtil
import com.ybmmarket20.R
import com.ybmmarket20.business.correction.adapter.PictureSelectorAdapter
import com.ybmmarket20.business.correction.ext.span
import com.ybmmarket20.business.correction.ui.activity.BaseCorrectionActivity
import com.ybmmarket20.business.correction.ui.activity.GoodsCorrectionActivity
import com.ybmmarket20.business.correction.ui.activity.PriceCorrectionActivity
import com.ybmmarket20.business.correction.watcher.CustomTextWatcher
import com.ybmmarket20.business.correction.widget.FullyGridLayoutManager
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.view.GridSpacingItemDecoration
import kotlinx.android.synthetic.main.fragment_correction_common.*
import java.util.*

/* 
 * Created by sean on 2019-08-19
 */

class CommonCorrectionFragment : BaseFragment(), PictureSelectorAdapter.OnStatusChangedListener, View.OnClickListener {

    private lateinit var adapter: PictureSelectorAdapter
    private lateinit var pop: PopupWindow
    private lateinit var from: From
    private var isOthers: Boolean = false
    private val selectList = ArrayList<LocalMedia>()

    override fun getLayoutId() = R.layout.fragment_correction_common

    override fun initData(content: String?) {
        initPictureComponent()
        initListener()
        setInstructionChangeListener()
    }

    override fun initTitle() {
    }

    override fun getParams(): RequestParams? = null

    override fun getUrl(): String? = null

    /**
     * 设置入口
     */
    fun setFrom(from: From) {
        this.from = from
    }

    /**
     * 是否已经添加图片和补充说明
     */
    fun isForcedParamsDone(): Boolean = selectList.isNotEmpty() && !TextUtils.isEmpty(instructionsEt.text)

    /**
     * 获取图片数据
     */
    fun getImages(): ArrayList<LocalMedia> = selectList

    /**
     * 获取补充说明
     */

    fun getInstruction(): String = instructionsEt.text.toString()

    /**
     * 当价格纠错时纠错原因切换为「其他」时,需要更新flag
     * flag  「true」 图片和补充说明非必填
     * flag  「false」 图片和补充说明必填
     */
    fun updateFlag(flag: Boolean) {
        this.isOthers = flag
    }

    /**
     * 补充说明和上传图片是否为必填项时标题状态
     * 「true」 必填
     * 「false」非必填
     */
    fun forcedParamsTitle(isForced: Boolean) {

        if (isForced) {
            instructionsTipsTv.text = SpannableString(resources.getString(R.string.instruction_goods)).span()
            uploadPhotoTipsTv.text = SpannableString(resources.getString(R.string.upload_picture)).span()
            instructionsEt.hint = resources.getString(R.string.correction_suggest)
        } else {
            instructionsTipsTv.text = resources.getString(R.string.instruction)
            uploadPhotoTipsTv.text = resources.getString(R.string.upload_photo)
            instructionsEt.hint = resources.getString(R.string.correction_optional)
        }

    }

    /**
     * 初始化图片选择部分
     */
    private fun initPictureComponent() {
        val layoutManager = FullyGridLayoutManager(activity, 3, GridLayoutManager.VERTICAL, false)
        val itemDecoration = GridSpacingItemDecoration(3, 10, 0, false)
        adapter = PictureSelectorAdapter()
        pickPhotoRv.layoutManager = layoutManager
        pickPhotoRv.addItemDecoration(itemDecoration)
        pickPhotoRv.adapter = adapter
        adapter.setOnStatusChangedListener(this)
    }

    /**
     * 初始化监听器
     */
    private fun initListener() {
        correctionSubmitTv.setOnClickListener(this)
    }

    /**
     * 展示图片选择pop
     */
    private fun showPop() {
        val popView = View.inflate(activity, R.layout.layout_correction_pic_dialog, null)
        val mAlbum = popView.findViewById<TextView>(R.id.tv_album)
        val mCamera = popView.findViewById<TextView>(R.id.tv_camera)
        val mCancel = popView.findViewById<TextView>(R.id.tv_cancel)
        pop = PopupWindow(popView, -1, -2)
        pop.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        pop.isOutsideTouchable = true
        pop.isFocusable = true
        val window = activity?.window
        window?.let {
            val layoutParams = window.attributes
            layoutParams.alpha = 0.5f
            window.attributes = layoutParams
        }

        pop.setOnDismissListener {
            window?.let {
                val lp = window.attributes
                lp.alpha = 1f
                window.attributes = lp
            }

        }
        pop.animationStyle = R.style.main_menu_photo_anim
        pop.showAtLocation(window?.decorView, Gravity.BOTTOM, 0, 0)
        mAlbum.setOnClickListener(this)
        mCamera.setOnClickListener(this)
        mCancel.setOnClickListener(this)
    }

    /**
     * 关闭图片选择pop
     */
    private fun closePopupWindow() {
        if (pop.isShowing) {
            pop.dismiss()
        }
    }

    /**
     * 提交按钮状态
     */
    fun submitEnable(enable: Boolean) =
            if (enable) {
                correctionSubmitTv.isEnabled = true
                correctionSubmitTv.setBackgroundDrawable(resources.getDrawable(R.drawable.shape_correction_submit_nomal))
            } else {
                correctionSubmitTv.isEnabled = false
                correctionSubmitTv.setBackgroundDrawable(resources.getDrawable(R.drawable.shape_correction_submit_disable))
            }

    /**
     * 设置补充说明文字内容变化监听
     */
    private fun setInstructionChangeListener() {
        instructionsEt.addTextChangedListener(object : CustomTextWatcher {
            override fun afterTextChanged(editable: Editable?) {
                val textLength = editable.toString().length
                instructionsTextSizeTv.text = if (textLength >= 200) {
                    SpannableString("$textLength/200").span(0, textLength.toString().length)
                } else {
                    "$textLength/200"
                }

                when (from) {
                    From.PRICE -> {

                        if (activity is PriceCorrectionActivity) {
                            val currentActivity = activity as PriceCorrectionActivity
                            if (currentActivity.getStatus()) {
                                submitEnable(isForcedParamsDone())
                            } else {
                                submitEnable(currentActivity.isForcedParamsDone())
                            }
                        }
                    }
                    From.GOODS -> {

                        if (activity is GoodsCorrectionActivity) {
                            val currentActivity = activity as GoodsCorrectionActivity
                            submitEnable(currentActivity.isReasonDone() && isForcedParamsDone())
                        }

                    }
                    From.OTHERS -> {
                        submitEnable(isForcedParamsDone())
                    }
                }
            }
        })
    }

    /**
     * 点击添加图片
     */
    @SuppressLint("CheckResult")
    override fun onItemAdd() {
        activity?.let {
            if (it.isDestroyed) return
            val rxPermissions = RxPermissions(it)
            if (rxPermissions.isGranted(Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                requestPhotoPermission()
            } else {
                PermissionDialogUtil.showPermissionInfoDialog(it,
                        "药帮忙App需要申请存储权限，用于保存图片"
                ) { requestPhotoPermission() }
            }
        }

    }

    private fun requestPhotoPermission() {
        activity?.let {
            if (it.isDestroyed) return
            (activity as BaseActivity).rxPermissions.requestEach(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                    .subscribe { permission ->
                        if (permission.granted) {// 用户已经同意该权限
                            showPop()
                        } else {
                            Toast.makeText(activity, "拒绝", Toast.LENGTH_SHORT).show()
                        }
                    }
        }
    }

    /**
     * 点击图片查看
     */
    override fun onItemClick(view: View, position: Int) {
        if (selectList.size > 0) {
            val media = selectList[position]
            val pictureType = media.pictureType
            val mediaType = PictureMimeType.pictureToVideo(pictureType)
            if (mediaType == 1) {// 预览图片
                PictureSelector.create(this).externalPicturePreview(position, selectList)
            }
        }
    }

    /**
     * 图片集合数据有变化
     */
    override fun onDataChanged() {
        if (from == From.PRICE) {
            if (isOthers) {
                submitEnable(isForcedParamsDone())
            } else {
                val currentActivity = activity as PriceCorrectionActivity
                submitEnable(currentActivity.isForcedParamsDone())
            }
        } else {
            submitEnable(isForcedParamsDone())
        }
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.tv_album -> {
                PictureSelector.create(this)
                        .openGallery(PictureMimeType.ofImage())
                        .maxSelectNum(3.minus(selectList.size))
                        .minSelectNum(1)
                        .imageSpanCount(4)
                        .compress(true)
                        .selectionMode(PictureConfig.MULTIPLE)
                        .forResult(PictureConfig.CHOOSE_REQUEST)
                closePopupWindow()
            }
            R.id.tv_camera -> {

                PictureSelector.create(this)
                        .openCamera(PictureMimeType.ofImage())
                        .compress(true)
                        .forResult(PictureConfig.CHOOSE_REQUEST)
                closePopupWindow()
            }
            R.id.tv_cancel -> {
                closePopupWindow()
            }

            R.id.correctionSubmitTv -> {
                (activity as? BaseCorrectionActivity)?.uploadInfo()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK && requestCode == PictureConfig.CHOOSE_REQUEST) {
            val images: List<LocalMedia> = PictureSelector.obtainMultipleResult(data)
            selectList.addAll(images)
            adapter.setData(selectList)
            adapter.notifyDataSetChanged()
        }
    }

    enum class From {
        PRICE, GOODS, OTHERS
    }

}