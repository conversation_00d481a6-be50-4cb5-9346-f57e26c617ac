package com.ybmmarket20.business.correction.ui.activity

import android.content.Intent
import android.view.View
import com.ybmmarket20.R
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.constant.IntentCanst
import kotlinx.android.synthetic.main.activity_correction_main.*

/* 
 * Created by sean on 2019-08-20
 */

class MainCorrectionActivity : BaseActivity(), View.OnClickListener {

    private var skuPrice: String? = ""
    private var skuId: String? = ""

    override fun getContentViewId() = R.layout.activity_correction_main;

    override fun initData() {
        setTitle(getString(R.string.goods_correction_main_title))
        val intent = intent
        skuPrice = intent.getStringExtra(IntentCanst.SKU_PRICE)
        skuId = intent.getStringExtra(IntentCanst.SKU_ID)
        priceTv.setOnClickListener(this@MainCorrectionActivity)
        goodsTv.setOnClickListener(this@MainCorrectionActivity)
        othersTv.setOnClickListener(this@MainCorrectionActivity)
    }

    override fun onClick(view: View) {
        var intent: Intent? = null
        when (view) {
            priceTv -> {
                intent = Intent(this@MainCorrectionActivity, PriceCorrectionActivity::class.java)
                intent.putExtra(IntentCanst.SKU_PRICE, skuPrice)
            }
            goodsTv -> intent = Intent(this@MainCorrectionActivity, GoodsCorrectionActivity::class.java)
            othersTv -> intent = Intent(this@MainCorrectionActivity, OthersCorrectionActivity::class.java)
        }
        intent?.also {
            it.putExtra(IntentCanst.SKU_ID, skuId)
            startActivity(it)
        }
    }

}