package com.ybmmarket20.business.correction.adapter

import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.luck.picture.lib.entity.LocalMedia
import com.ybmmarket20.R
import java.util.*

/*
 * Created by sean on 2019-08-19
 */

class PictureSelectorAdapter : RecyclerView.Adapter<PictureSelectorAdapter.ViewHolder>() {

    companion object {

        const val TYPE_CAMERA = 1
        const val TYPE_PICTURE = 2

        const val SELECT_MAX = 3
    }

    private var list = ArrayList<LocalMedia>()
    private var onStatusChangedListener: OnStatusChangedListener? = null

    fun setOnStatusChangedListener(onStatusChangedListener: OnStatusChangedListener) {
        this.onStatusChangedListener = onStatusChangedListener
    }

    fun setData(localMedias: ArrayList<LocalMedia>) {
        this.list = localMedias
        onStatusChangedListener?.onDataChanged()
    }


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_correction_pic, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount(): Int = if (list.size < SELECT_MAX) list.size.plus(1) else list.size

    override fun getItemViewType(position: Int): Int = if (isShowItem(position)) TYPE_CAMERA else TYPE_PICTURE

    private fun isShowItem(position: Int): Boolean = position == list.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (getItemViewType(position) == TYPE_CAMERA) {
            holder.picture.setImageResource(R.drawable.icon_correction_pic_add)
            holder.picture.setOnClickListener { onStatusChangedListener?.onItemAdd() }
            holder.delete.visibility = View.INVISIBLE
        } else {
            holder.delete.visibility = View.VISIBLE
            holder.delete.setOnClickListener {
                val adapterPosition = holder.adapterPosition
                if (adapterPosition != RecyclerView.NO_POSITION) {
                    list.removeAt(adapterPosition)
                    notifyItemRemoved(adapterPosition)
                    notifyItemRangeChanged(adapterPosition, list.size)
                    onStatusChangedListener?.onDataChanged()
                }
            }

            val media = list[position]
            Glide.with(holder.itemView.context)
                    .load(if (media.isCompressed || media.isCut && media.isCompressed) {
                        media.compressPath
                    } else {
                        media.path
                    })
                    .centerCrop()
                    .placeholder(R.color.color_f6f6f6)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .into(holder.picture)
            holder.itemView.setOnClickListener { view -> onStatusChangedListener?.onItemClick(view, holder.adapterPosition) }
        }
    }


    inner class ViewHolder internal constructor(view: View) : RecyclerView.ViewHolder(view) {

        internal var picture: ImageView = view.findViewById(R.id.fiv)
        internal var delete: LinearLayout = view.findViewById(R.id.ll_del)

    }


    interface OnStatusChangedListener {

        fun onItemAdd()

        fun onItemClick(view: View, position: Int)

        fun onDataChanged()
    }
}
