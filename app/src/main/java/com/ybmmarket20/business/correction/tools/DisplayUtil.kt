package com.ybmmarket20.business.correction.tools

import android.content.res.Resources
import android.util.TypedValue
import android.view.View

/* 
 * Created by sean on 2019-08-20
 */

fun dp2px(value: Float): Int {
    return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, value, Resources.getSystem().displayMetrics).toInt()
}

fun modifyVisibility(status: Int, vararg views: View) {
    views.forEach {
        it.visibility = status
    }
}