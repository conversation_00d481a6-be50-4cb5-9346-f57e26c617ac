package com.ybmmarket20.business.correction.widget;
/*
 * Created by sean on 2019-08-07
 * 价格纠错-纠错原因
 */

import android.content.Context;
import androidx.annotation.NonNull;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;

import com.ybmmarket20.R;
import com.ybmmarket20.view.TransparentDialog;

public class PriceCorrectionReasonDialog extends TransparentDialog {


    private RadioGroup priceSuggestRg;
    private TextView priceCancelTv;

    public PriceCorrectionReasonDialog(@NonNull Context context) {
        super(context);
        initView();
        initListener();
    }

    private void initView() {
        priceSuggestRg = ((RadioGroup) findViewById(R.id.priceSuggestRg));
        priceCancelTv = ((TextView) findViewById(R.id.priceCancelTv));
        priceSuggestRg.check(R.id.priceHighSideTv);
    }

    private void initListener() {
        priceCancelTv.setOnClickListener(v -> {
            dismiss();
        });

        priceSuggestRg.setOnCheckedChangeListener((group, checkedId) -> {
            switch (checkedId) {
                case R.id.priceHighSideTv:
                case R.id.priceErrorTv:
                case R.id.priceOthersTv:
                    setInfo();
                    break;
            }
        });
    }

    private void setInfo() {
        int checkedRadioButtonId = priceSuggestRg.getCheckedRadioButtonId();
        ItemInfo itemInfo = new ItemInfo();
        RadioButton currentRb = (RadioButton) findViewById(checkedRadioButtonId);
        currentRb.setChecked(true);
        itemInfo.itemName = currentRb.getText().toString();
        listener.itemSelect(itemInfo);
        dismiss();
    }

    @Override
    public int getLayoutId() {
        return R.layout.dialog_choice_price_correction_reason;
    }

}
